"use client"

import { useEffect, useState } from "react"
import { AnalyticsDashboard } from "@/components/Analytics/AnalyticsDashboard"
import { Container } from "@/components/UI/Container"
import { ErrorBoundary } from "@/components/UI/ErrorBoundary"
import { LoadingSpinner } from "@/components/UI/LoadingSpinner"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"
import { linksConfig } from "@/data/links"
import type { AnalyticsData } from "@/types"

export default function AnalyticsPage() {
  const router = useRouter()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading and load analytics from localStorage
    const timer = setTimeout(() => {
      const stored = localStorage.getItem("link-bio-analytics")
      if (stored) {
        try {
          setAnalytics(JSON.parse(stored))
        } catch (error) {
          console.error("Failed to parse analytics data:", error)
          setAnalytics({
            totalClicks: 0,
            linkClicks: {},
            lastUpdated: new Date().toISOString(),
          })
        }
      } else {
        setAnalytics({
          totalClicks: 0,
          linkClicks: {},
          lastUpdated: new Date().toISOString(),
        })
      }
      setIsLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  return (
    <ErrorBoundary>
      <main className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <Container maxWidth="xl" className="py-6 sm:py-8 lg:py-12">
          <div className="mb-4 sm:mb-6">
            <Button
              variant="ghost"
              onClick={() => router.push("/")}
              className="mb-4 touch-manipulation min-h-[44px]"
              aria-label="Go back to main page"
            >
              <ArrowLeft className="size-4 mr-2" aria-hidden="true" />
              <span className="text-sm sm:text-base">Back to Links</span>
            </Button>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner />
            </div>
          ) : analytics ? (
            <AnalyticsDashboard analytics={analytics} links={linksConfig} />
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">Unable to load analytics data.</p>
            </div>
          )}
        </Container>
      </main>
    </ErrorBoundary>
  )
}
