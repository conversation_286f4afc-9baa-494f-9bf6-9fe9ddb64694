import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Geist<PERSON>ans } from "geist/font/sans"
import { Geist<PERSON>ono } from "geist/font/mono"
import "./globals.css"

export const metadata: Metadata = {
  title: "AiHacksDailyy | Link in Bio - Tech Content Creator & AI Tools",
  description:
    "Tech Content Creator sharing AI tools, tips & tutorials. Find all my recommended tools, tutorials, and resources in one place.",
  keywords: ["AI tools", "tech content creator", "AI tutorials", "Instagram", "tech tips", "artificial intelligence"],
  authors: [{ name: "AiHacksDailyy" }],
  creator: "AiHacksDailyy",
  openGraph: {
    title: "AiHacksDailyy | Link in Bio",
    description: "Tech Content Creator sharing AI tools, tips & tutorials",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "AiHacksDailyy | Link in Bio",
    description: "Tech Content Creator sharing AI tools, tips & tutorials",
  },
  robots: {
    index: true,
    follow: true,
  },
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable} antialiased`}>
        <div id="root">{children}</div>
      </body>
    </html>
  )
}
