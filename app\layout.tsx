import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { GeistSans } from "geist/font/sans"
import { GeistMono } from "geist/font/mono"
import "./globals.css"

export const metadata: Metadata = {
  title: "<PERSON> | Link in Bio - Content Creator & Design Resources",
  description:
    "Content Creator sharing design tools and creative resources. Find all my recommended tools, tutorials, and resources in one place.",
  keywords: ["design tools", "content creator", "creative resources", "Instagram", "design tutorials"],
  authors: [{ name: "<PERSON>" }],
  creator: "<PERSON>",
  openGraph: {
    title: "<PERSON> | Link in Bio",
    description: "Content Creator sharing design tools and creative resources",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "<PERSON> | Link in Bio",
    description: "Content Creator sharing design tools and creative resources",
  },
  robots: {
    index: true,
    follow: true,
  },
  generator: "v0.app",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable} antialiased`}>
        <div id="root">{children}</div>
      </body>
    </html>
  )
}
