"use client"

import { Suspense } from "react"
import { ProfileHeader } from "@/components/Profile/ProfileHeader"
import { SocialLinks } from "@/components/Profile/SocialLinks"
import { LinkGrid } from "@/components/Links/LinkGrid"
import { ClickTracker } from "@/components/Analytics/ClickTracker"
import { Container } from "@/components/ui/Container"
import { ErrorBoundary } from "@/components/ui/ErrorBoundary"
import { SkeletonCard } from "@/components/ui/SkeletonCard"
import { Button } from "@/components/ui/button"
import { BarChart3 } from "lucide-react"
import { useRouter } from "next/navigation"
import { profileConfig } from "@/data/profile"
import { linksConfig, categoriesConfig } from "@/data/links"

function LinkGridSkeleton() {
  return (
    <div className="space-y-6 sm:space-y-8">
      <div className="space-y-3 sm:space-y-4">
        <div className="flex items-center gap-2">
          <div className="size-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
          <div className="h-6 w-20 bg-muted rounded animate-pulse"></div>
        </div>
        <div className="grid gap-3 sm:gap-4">
          <SkeletonCard />
          <SkeletonCard />
        </div>
      </div>
    </div>
  )
}

export default function HomePage() {
  const router = useRouter()

  return (
    <ErrorBoundary>
      <main className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <Container maxWidth="md" className="py-6 sm:py-8 lg:py-12">
          <ProfileHeader profile={profileConfig} />
          <SocialLinks profile={profileConfig} className="mb-6 sm:mb-8" />

          {/* Analytics Access Button */}
          <div className="flex justify-center mb-4 sm:mb-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push("/analytics")}
              className="text-muted-foreground hover:text-purple-600 touch-manipulation min-h-[40px]"
              aria-label="View analytics dashboard"
            >
              <BarChart3 className="size-4 mr-2" aria-hidden="true" />
              <span className="text-sm">View Analytics</span>
            </Button>
          </div>

          {/* Links with Analytics Tracking */}
          <ErrorBoundary
            fallback={
              <div className="text-center py-8">
                <p className="text-muted-foreground">Unable to load links. Please refresh the page.</p>
              </div>
            }
          >
            <Suspense fallback={<LinkGridSkeleton />}>
              <ClickTracker links={linksConfig}>
                {(trackClick, analytics) => (
                  <LinkGrid
                    links={linksConfig}
                    categories={categoriesConfig}
                    onLinkClick={(linkId) => {
                      console.log(`[v0] Link clicked: ${linkId}`)
                      trackClick(linkId)
                    }}
                  />
                )}
              </ClickTracker>
            </Suspense>
          </ErrorBoundary>
        </Container>
      </main>
    </ErrorBoundary>
  )
}
