"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Bar<PERSON>hart<PERSON>, Trending<PERSON><PERSON>, Mouse<PERSON>ointer, Calendar } from "lucide-react"
import { formatClickCount, getTopLinks } from "@/utils/analytics"
import type { AnalyticsData, Link } from "@/types"

interface AnalyticsDashboardProps {
  analytics: AnalyticsData
  links: Link[]
  onClose?: () => void
}

export function AnalyticsDashboard({ analytics, links, onClose }: AnalyticsDashboardProps) {
  const topLinks = getTopLinks(analytics.linkClicks, 5)
  const lastUpdated = new Date(analytics.lastUpdated).toLocaleDateString()

  const getLinkById = (linkId: string) => {
    return links.find((link) => link.id === linkId)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BarChart3 className="size-6 text-purple-600" />
          <h2 className="text-2xl font-bold text-foreground">Analytics Dashboard</h2>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        )}
      </div>

      {/* Overview Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <MousePointer className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{analytics.totalClicks}</div>
            <p className="text-xs text-muted-foreground">All time clicks</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Links</CardTitle>
            <TrendingUp className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{Object.keys(analytics.linkClicks).length}</div>
            <p className="text-xs text-muted-foreground">Links with clicks</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
            <Calendar className="size-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{lastUpdated}</div>
            <p className="text-xs text-muted-foreground">Data freshness</p>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Links */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="size-5" />
            Top Performing Links
          </CardTitle>
        </CardHeader>
        <CardContent>
          {topLinks.length > 0 ? (
            <div className="space-y-4">
              {topLinks.map(({ linkId, clicks }, index) => {
                const link = getLinkById(linkId)
                if (!link) return null

                return (
                  <div key={linkId} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                    <div className="flex items-center gap-3">
                      <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center">
                        {index + 1}
                      </Badge>
                      <div>
                        <h4 className="font-medium text-foreground">{link.title}</h4>
                        <p className="text-sm text-muted-foreground truncate max-w-md">{link.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-purple-600">{formatClickCount(clicks)}</div>
                      {link.isNew && (
                        <Badge variant="outline" className="text-xs mt-1">
                          NEW
                        </Badge>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <MousePointer className="size-12 mx-auto mb-4 opacity-50" />
              <p>No clicks recorded yet</p>
              <p className="text-sm">Start sharing your links to see analytics!</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* All Links Performance */}
      <Card>
        <CardHeader>
          <CardTitle>All Links Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {links.map((link) => {
              const clicks = analytics.linkClicks[link.id] || 0
              return (
                <div
                  key={link.id}
                  className="flex items-center justify-between py-2 border-b border-border/50 last:border-0"
                >
                  <div className="flex-1">
                    <h5 className="font-medium text-sm">{link.title}</h5>
                    <p className="text-xs text-muted-foreground">{link.category}</p>
                  </div>
                  <div className="text-sm font-medium text-muted-foreground">
                    {clicks > 0 ? formatClickCount(clicks) : "No clicks"}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
