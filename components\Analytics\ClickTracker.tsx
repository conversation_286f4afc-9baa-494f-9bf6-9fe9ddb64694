"use client"

import type React from "react"

import { useAnalytics } from "@/hooks/useAnalytics"
import type { <PERSON> } from "@/types"

interface ClickTrackerProps {
  links: Link[]
  children: (trackClick: (linkId: string) => void, analytics: any) => React.ReactNode
}

export function ClickTracker({ links, children }: ClickTrackerProps) {
  const { analytics, trackClick } = useAnalytics()

  return <>{children(trackClick, analytics)}</>
}
