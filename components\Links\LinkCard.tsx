"use client"

import type React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Sparkles } from "lucide-react"
import type { Link, LinkCategory } from "@/types"

interface LinkCardProps {
  link: Link
  category: LinkCategory
  onClick: (linkId: string) => void
}

export function LinkCard({ link, category, onClick }: LinkCardProps) {
  const handleClick = () => {
    onClick(link.id)
    window.open(link.url, "_blank", "noopener,noreferrer")
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault()
      handleClick()
    }
  }

  const getCategoryColor = (color: string) => {
    const colors = {
      purple: "bg-purple-100 text-purple-700 border-purple-200",
      blue: "bg-blue-100 text-blue-700 border-blue-200",
      green: "bg-green-100 text-green-700 border-green-200",
      orange: "bg-orange-100 text-orange-700 border-orange-200",
      red: "bg-red-100 text-red-700 border-red-200",
      yellow: "bg-yellow-100 text-yellow-700 border-yellow-200",
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <Card
      className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-purple-100/50 hover:-translate-y-1 border-border/50 hover:border-purple-200/50 bg-white/80 backdrop-blur-sm active:scale-95 touch-manipulation focus-within:ring-2 focus-within:ring-purple-500 focus-within:ring-offset-2"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Open ${link.title} - ${link.description}`}
    >
      <CardContent className="p-4 sm:p-5 lg:p-6">
        <div className="flex items-start justify-between gap-3 sm:gap-4">
          <div className="flex-1 space-y-2 sm:space-y-3">
            {/* Title and New Badge */}
            <div className="flex items-center gap-2 flex-wrap">
              <h3 className="font-semibold text-foreground group-hover:text-purple-700 transition-colors text-balance leading-tight text-sm sm:text-base">
                {link.title}
              </h3>
              {link.isNew && (
                <Badge
                  variant="secondary"
                  className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 text-xs px-2 py-0.5"
                  aria-label="New link"
                >
                  <Sparkles className="size-3 mr-1" aria-hidden="true" />
                  NEW
                </Badge>
              )}
            </div>

            {/* Description */}
            <p className="text-xs sm:text-sm text-muted-foreground text-pretty leading-relaxed">{link.description}</p>

            {/* Category Badge */}
            <div className="flex items-center justify-between">
              <Badge
                variant="outline"
                className={`text-xs ${getCategoryColor(category.color)}`}
                aria-label={`Category: ${category.name}`}
              >
                {category.name}
              </Badge>

              {link.clickCount > 0 && (
                <span className="text-xs text-muted-foreground" aria-label={`${link.clickCount} clicks`}>
                  {link.clickCount} click{link.clickCount !== 1 ? "s" : ""}
                </span>
              )}
            </div>
          </div>

          {/* External Link Icon */}
          <div className="flex-shrink-0">
            <ExternalLink
              className="size-4 sm:size-5 text-muted-foreground group-hover:text-purple-600 transition-colors"
              aria-hidden="true"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
