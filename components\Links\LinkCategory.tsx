"use client"

import { useState } from "react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import {
  ChevronDown,
  Star,
  Wrench,
  BookOpen,
  Video,
  Palette,
  Type,
  Layers,
  Figma,
  Brain as Gradient,
} from "lucide-react"
import { LinkCard } from "./LinkCard"
import type { Link, LinkCategory as LinkCategoryType } from "@/types"

interface LinkCategoryProps {
  category: LinkCategoryType
  links: Link[]
  onLinkClick: (linkId: string) => void
  defaultOpen?: boolean
}

export function LinkCategory({ category, links, onLinkClick, defaultOpen = false }: LinkCategoryProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  const getIcon = (iconName: string) => {
    const icons = {
      star: Star,
      wrench: Wrench,
      "book-open": BookOpen,
      video: Video,
      palette: Palette,
      type: Type,
      layers: Layers,
      figma: Figma,
      gradient: Gradient,
    }
    const IconComponent = icons[iconName as keyof typeof icons] || Star
    return <IconComponent className="size-4" aria-hidden="true" />
  }

  const getCategoryColor = (color: string) => {
    const colors = {
      purple: "text-purple-600",
      blue: "text-blue-600",
      green: "text-green-600",
      orange: "text-orange-600",
      red: "text-red-600",
      yellow: "text-yellow-600",
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  if (links.length === 0) return null

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className="space-y-3">
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-between p-3 sm:p-4 h-auto hover:bg-accent/50 transition-colors text-left"
          aria-expanded={isOpen}
          aria-controls={`category-${category.id}`}
          aria-label={`${isOpen ? "Collapse" : "Expand"} ${category.name} category with ${links.length} links`}
        >
          <div className="flex items-center gap-3">
            <div className={getCategoryColor(category.color)}>{getIcon(category.icon)}</div>
            <span className="font-semibold text-foreground text-sm sm:text-base">{category.name}</span>
            <span className="text-xs sm:text-sm text-muted-foreground">({links.length})</span>
          </div>
          <ChevronDown
            className={`size-4 text-muted-foreground transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
            aria-hidden="true"
          />
        </Button>
      </CollapsibleTrigger>

      <CollapsibleContent
        className="space-y-3 animate-in slide-in-from-top-2 duration-200"
        id={`category-${category.id}`}
      >
        <div
          className="grid gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-1"
          role="list"
          aria-label={`${category.name} links`}
        >
          {links.map((link) => (
            <div key={link.id} role="listitem">
              <LinkCard link={link} category={category} onClick={onLinkClick} />
            </div>
          ))}
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
