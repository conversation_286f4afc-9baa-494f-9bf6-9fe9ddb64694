"use client"

import { LinkCategory } from "./LinkCategory"
import { LinkCard } from "./LinkCard"
import type { Link, LinkCategory as LinkCategoryType } from "@/types"

interface LinkGridProps {
  links: Link[]
  categories: LinkCategoryType[]
  onLinkClick?: (linkId: string) => void
}

export function LinkGrid({ links, categories, onLinkClick = () => {} }: LinkGridProps) {
  // Get featured links
  const featuredLinks = links.filter((link) => link.featured)

  // Group links by category
  const linksByCategory = categories.reduce(
    (acc, category) => {
      const categoryLinks = links.filter((link) => link.category === category.id && !link.featured)
      if (categoryLinks.length > 0) {
        acc[category.id] = categoryLinks
      }
      return acc
    },
    {} as Record<string, Link[]>,
  )

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Featured Links Section */}
      {featuredLinks.length > 0 && (
        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center gap-2">
            <div className="size-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
            <h2 className="text-lg sm:text-xl font-semibold text-foreground">Featured</h2>
          </div>
          <div className="grid gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-1">
            {featuredLinks.map((link) => {
              const category = categories.find((cat) => cat.id === link.category)
              return category ? <LinkCard key={link.id} link={link} category={category} onClick={onLinkClick} /> : null
            })}
          </div>
        </div>
      )}

      {/* Categorized Links */}
      <div className="space-y-4 sm:space-y-6">
        {categories.map((category) => {
          const categoryLinks = linksByCategory[category.id]
          return categoryLinks ? (
            <LinkCategory
              key={category.id}
              category={category}
              links={categoryLinks}
              onLinkClick={onLinkClick}
              defaultOpen={category.id === "latest"}
            />
          ) : null
        })}
      </div>

      {/* Empty State */}
      {links.length === 0 && (
        <div className="text-center py-8 sm:py-12">
          <p className="text-muted-foreground text-sm sm:text-base">No links available yet.</p>
        </div>
      )}
    </div>
  )
}
