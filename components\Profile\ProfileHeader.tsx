"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Instagram, ExternalLink } from "lucide-react"
import type { Profile } from "@/types"

interface ProfileHeaderProps {
  profile: Profile
}

export function ProfileHeader({ profile }: ProfileHeaderProps) {
  const handleInstagramClick = () => {
    window.open(profile.instagramUrl, "_blank", "noopener,noreferrer")
  }

  return (
    <header className="flex flex-col items-center text-center space-y-4 sm:space-y-6 mb-8 sm:mb-12">
      {/* Name and Bio */}
      <div className="space-y-2 sm:space-y-3 max-w-sm sm:max-w-md lg:max-w-lg px-4">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground text-balance">{profile.name}</h1>
        <p className="text-sm sm:text-base text-muted-foreground text-pretty leading-relaxed">{profile.bio}</p>
      </div>

      {/* Instagram Handle Button */}
      <Button
        onClick={handleInstagramClick}
        variant="outline"
        size="default"
        className="group hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 hover:text-white hover:border-transparent transition-all duration-300 bg-transparent touch-manipulation min-h-[44px] px-4 sm:px-6"
        aria-label={`Follow ${profile.name} on Instagram`}
      >
        <Instagram className="size-4" aria-hidden="true" />
        <span className="text-sm sm:text-base">{profile.instagramHandle}</span>
        <ExternalLink className="size-3 opacity-60 group-hover:opacity-100" aria-hidden="true" />
      </Button>
    </header>
  )
}
