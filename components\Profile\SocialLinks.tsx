"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Instagram, ExternalLink, Heart } from "lucide-react"
import type { Profile } from "@/types"

interface SocialLinksProps {
  profile: Profile
  className?: string
}

export function SocialLinks({ profile, className }: SocialLinksProps) {
  const handleInstagramClick = () => {
    window.open(profile.instagramUrl, "_blank", "noopener,noreferrer")
  }

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {/* Main Instagram Link */}
      <Button
        onClick={handleInstagramClick}
        variant="default"
        size="lg"
        className="w-full max-w-sm bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
      >
        <Instagram className="size-5" />
        Follow on Instagram
        <ExternalLink className="size-4 opacity-80" />
      </But<PERSON>>

      {/* Additional Social Links Section */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Heart className="size-4 text-red-500" />
        <span>Connect with me on social media</span>
      </div>
    </div>
  )
}
