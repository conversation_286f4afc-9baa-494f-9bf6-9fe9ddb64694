export function SkeletonCard() {
  return (
    <div className="bg-white/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 sm:p-5 lg:p-6 animate-pulse">
      <div className="flex items-start justify-between gap-3 sm:gap-4">
        <div className="flex-1 space-y-2 sm:space-y-3">
          <div className="flex items-center gap-2">
            <div className="h-4 sm:h-5 bg-muted rounded w-3/4"></div>
            <div className="h-5 w-12 bg-gradient-to-r from-purple-200 to-pink-200 rounded"></div>
          </div>
          <div className="h-3 sm:h-4 bg-muted rounded w-full"></div>
          <div className="h-3 sm:h-4 bg-muted rounded w-2/3"></div>
          <div className="flex items-center justify-between">
            <div className="h-5 w-20 bg-muted rounded"></div>
            <div className="h-3 w-16 bg-muted rounded"></div>
          </div>
        </div>
        <div className="w-4 sm:w-5 h-4 sm:h-5 bg-muted rounded"></div>
      </div>
    </div>
  )
}
