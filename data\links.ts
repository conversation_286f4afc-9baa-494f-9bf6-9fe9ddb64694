import type { <PERSON>, LinkCategory } from "@/types"

export const categoriesConfig: LinkCategory[] = [
  {
    id: "featured",
    name: "Featured",
    color: "purple",
    icon: "star",
  },
  {
    id: "tools",
    name: "Design Tools",
    color: "blue",
    icon: "wrench",
  },
  {
    id: "resources",
    name: "Resources",
    color: "green",
    icon: "book-open",
  },
  {
    id: "latest",
    name: "Latest Reel",
    color: "orange",
    icon: "video",
  },
]

export const linksConfig: Link[] = [
  {
    id: "figma-plugin",
    title: "Amazing Figma Plugin",
    description: "The design plugin I mentioned in my latest reel - saves hours of work!",
    url: "https://figma.com/community/plugin/example",
    category: "featured",
    featured: true,
    isNew: true,
    icon: "figma",
    clickCount: 0,
  },
  {
    id: "color-palette",
    title: "Free Color Palette Generator",
    description: "Generate beautiful color palettes for your designs",
    url: "https://coolors.co",
    category: "tools",
    featured: false,
    isNew: false,
    icon: "palette",
    clickCount: 0,
  },
  {
    id: "typography-guide",
    title: "Typography Best Practices",
    description: "Complete guide to choosing and pairing fonts",
    url: "https://example.com/typography-guide",
    category: "resources",
    featured: false,
    isNew: false,
    icon: "type",
    clickCount: 0,
  },
  {
    id: "latest-tutorial",
    title: "How to Create Stunning Gradients",
    description: "Step-by-step tutorial from my latest Instagram reel",
    url: "https://example.com/gradient-tutorial",
    category: "latest",
    featured: true,
    isNew: true,
    icon: "gradient",
    clickCount: 0,
  },
  {
    id: "design-system",
    title: "Free Design System Template",
    description: "Complete design system template for Figma",
    url: "https://example.com/design-system",
    category: "resources",
    featured: false,
    isNew: false,
    icon: "layers",
    clickCount: 0,
  },
]
