"use client"

import { useState, useEffect } from "react"
import type { AnalyticsData } from "@/types"

const ANALYTICS_KEY = "link-bio-analytics"

export function useAnalytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalClicks: 0,
    linkClicks: {},
    lastUpdated: new Date().toISOString(),
  })

  useEffect(() => {
    // Load analytics from localStorage on mount
    const stored = localStorage.getItem(ANALYTICS_KEY)
    if (stored) {
      try {
        setAnalytics(JSON.parse(stored))
      } catch (error) {
        console.error("Failed to parse analytics data:", error)
      }
    }
  }, [])

  const trackClick = (linkId: string) => {
    setAnalytics((prev) => {
      const newAnalytics = {
        totalClicks: prev.totalClicks + 1,
        linkClicks: {
          ...prev.linkClicks,
          [linkId]: (prev.linkClicks[linkId] || 0) + 1,
        },
        lastUpdated: new Date().toISOString(),
      }

      // Save to localStorage
      localStorage.setItem(ANALYTICS_KEY, JSON.stringify(newAnalytics))
      return newAnalytics
    })
  }

  return { analytics, trackClick }
}
