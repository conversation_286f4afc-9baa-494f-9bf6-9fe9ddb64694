export interface Profile {
  name: string
  bio: string
  avatar: string
  instagramHandle: string
  instagramUrl: string
}

export interface Link {
  id: string
  title: string
  description: string
  url: string
  category: string
  featured: boolean
  isNew: boolean
  icon?: string
  clickCount: number
}

export interface LinkCategory {
  id: string
  name: string
  color: string
  icon: string
}

export interface AnalyticsData {
  totalClicks: number
  linkClicks: Record<string, number>
  lastUpdated: string
}
