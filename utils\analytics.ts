export function formatClickCount(count: number): string {
  if (count === 0) return "0 clicks"
  if (count === 1) return "1 click"
  if (count < 1000) return `${count} clicks`
  if (count < 1000000) return `${(count / 1000).toFixed(1)}k clicks`
  return `${(count / 1000000).toFixed(1)}M clicks`
}

export function getTopLinks(linkClicks: Record<string, number>, limit = 5) {
  return Object.entries(linkClicks)
    .sort(([, a], [, b]) => b - a)
    .slice(0, limit)
    .map(([linkId, clicks]) => ({ linkId, clicks }))
}
